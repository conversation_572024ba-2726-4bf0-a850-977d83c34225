use anyhow::{Context, Result};
use chrono::Local;
use clap::Parser;
use gethostname::gethostname;
use gui::LabrecordApp;
use serde::Deserialize;
use std::fs;
use std::io::{stdout, Write};
use std::path::PathBuf;
use std::sync::atomic::AtomicBool;
use std::sync::Arc;
use std::time::Duration;
use tokio::io::AsyncRead;
use tokio_serial::{self, SerialPortType};

use csv_logger::CsvLogger;

mod csv_logger;
mod gui;
mod packet;
mod sample;
mod serial;
mod wire;

// Constants
const BAUD_RATE: u32 = 115200;

#[derive(Parser, Debug)]
#[command(author, version, about = "Record ADXL345 accelerometer data")]
struct Cli {}

#[derive(Debug, Deserialize)]
pub struct Config {
    port: String,
    scenario: String,
}

fn main() {
    // Set up Ctrl+C handler
    let running = Arc::new(std::sync::atomic::AtomicBool::new(true));
    let r = running.clone();
    ctrlc::set_handler(move || {
        eprintln!("\nReceived Ctrl+C, shutting down...");
        r.store(false, std::sync::atomic::Ordering::SeqCst);
        std::thread::sleep(Duration::from_millis(1500));
        std::process::exit(0);
    })
    .context("Error setting Ctrl+C handler")
    .unwrap();

    let gui = gui::LabrecordApp::default();

    let gui2 = gui.clone();
    let running2 = running.clone();

    std::thread::Builder::new()
        .name("data-recorder".to_string())
        .spawn(move || {
            let rt = tokio::runtime::Runtime::new()
                .context("Failed to create Tokio runtime")
                .unwrap();
            rt.block_on(app_loop(gui2, running2));
        })
        .unwrap();

    gui::run_gui(gui);

    running.store(false, std::sync::atomic::Ordering::SeqCst);
}

async fn app_loop(gui: LabrecordApp, running: Arc<AtomicBool>) {
    while let Err(e) = app(gui.clone(), running.clone()).await {
        eprintln!("Error in main app: {:?}", e);
    }
}

async fn app(gui: LabrecordApp, running: Arc<AtomicBool>) -> Result<()> {
    let _args = Cli::parse();
    let config = fs::read_to_string("config.toml").context("Failed to read config file")?;
    let config: Config = toml::from_str(&config).context("Failed to parse config file")?;

    let mut stdout = stdout().lock();

    let scenario_start = Local::now();
    let scenario_start = scenario_start.format("%Y-%m-%d_%H-%M-%S").to_string();

    'outer: while running.load(std::sync::atomic::Ordering::SeqCst) {
        // Connect to the serial port
        let stream: Box<dyn AsyncRead + Unpin + Send> = {
            writeln!(
                stdout,
                "Opening serial port {} at {BAUD_RATE} baud",
                config.port
            )?;
            Box::new(
                match tokio_serial::SerialStream::open(&tokio_serial::new(&config.port, BAUD_RATE))
                    .context("Failed to open serial port")
                {
                    Ok(stream) => stream,
                    Err(e) => {
                        writeln!(stdout, "Error opening serial port: {}", e)?;
                        let ports = tokio_serial::available_ports()?
                            .iter()
                            .filter(|p| matches!(p.port_type, SerialPortType::UsbPort(_)))
                            .map(|p| p.port_name.clone())
                            .collect::<Vec<_>>();
                        writeln!(stdout, "Available ports: {:?}", ports)?;
                        tokio::time::sleep(Duration::from_secs(2)).await;
                        writeln!(stdout, "Retrying...")?;
                        continue 'outer;
                    }
                },
            )
        };
        let mut stream = tokio::io::BufReader::new(stream);
        writeln!(stdout, "Connected!")?;

        // Give the firmware some time to stabilize before starting data collection
        writeln!(stdout, "Waiting 1 seconds for firmware to stabilize...")?;
        tokio::time::sleep(Duration::from_secs(1)).await;

        // Create the CSV logger
        // ensure that we do not reuse the same filename
        let batch_start = Local::now();
        let batch_start = batch_start.format("%Y-%m-%d_%H-%M-%S").to_string();

        let output_path = PathBuf::from(format!("readings/readings-{}.csv", batch_start));
        println!("Logging to {}", output_path.display());
        tokio::fs::write(
            output_path.with_extension("json"),
            serde_json::json!({
                "scenario": config.scenario,
                "batch_start": batch_start,
                "scenario_start": scenario_start,
                "hostname": gethostname().to_string_lossy(),
                "source": config.port,
            })
            .to_string(),
        )
        .await
        .context("Failed to write metadata JSON")?;
        let mut csv_logger = CsvLogger::new(&output_path).await?;

        if let Err(e) = serial::do_batch(&mut stream, &mut csv_logger, &gui, PACKET_SIZE).await {
            writeln!(stdout, "Error while collecting batch: {}", e)?;
            let _ = csv_logger.flush().await;
            continue 'outer;
        }

        let _ = csv_logger.flush().await;
        writeln!(stdout, "Batch complete")?;
    }

    writeln!(stdout, "Shutting down...")?;

    Ok(())
}
