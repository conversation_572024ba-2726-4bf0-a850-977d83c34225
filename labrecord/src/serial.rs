use anyhow::{Context, Result};
use egui_plot::PlotPoint;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::io::{AsyncRead, AsyncReadExt};
use tokio::sync::Mutex;

use crate::csv_logger::CsvLogger;
use crate::gui::{LabrecordApp, SensorData};

use crate::sample::{packet_to_samples, Sample};
use crate::wire;

// Constants
const BURST_SAMPLES: usize = 16;
const SAMPLES_PER_SECOND: usize = 800;
const TIME_PER_SAMPLE: Duration = Duration::from_millis(1000 / SAMPLES_PER_SECOND as u64);
const PACKET_SIZE: usize = 108; // Total size of UART packet

/// Debug function to print packet data in hex and ASCII
pub fn data_print(packet: &[u8]) {
    for chunk in packet.chunks(48) {
        // print packet in hex and ascii, with hex and ascii perfectly aligned (one line each)
        for byte in chunk.iter() {
            print!("{:02X} ", byte);
        }
        println!();
        for byte in chunk.iter() {
            print!(
                " {} ",
                if byte.is_ascii_graphic() {
                    *byte as char
                } else {
                    '.'
                }
            );
        }
        println!();
    }
}

/// Add samples to sensor data for GUI visualization
pub async fn add_samples(sensor: &Arc<Mutex<SensorData>>, time_offset: Duration, sample: &Sample) {
    let mut sensor = sensor.lock().await;
    sensor.x.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.x as f64,
    });
    sensor.y.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.y as f64,
    });
    sensor.z.push(PlotPoint {
        x: time_offset.as_secs_f64(),
        y: sample.z as f64,
    });
}

/// Synchronize to packet boundary by finding "ADXL" header
pub async fn synchronize_to_packet_boundary(stream: &mut (dyn AsyncRead + Unpin)) -> Result<()> {
    println!("Synchronizing to packet boundary...");
    let mut packet_buffer = vec![0u8; PACKET_SIZE];
    stream.read_exact(&mut packet_buffer).await?;
    let Some(start) = packet_buffer.windows(4).position(|w| w == b"ADXL") else {
        anyhow::bail!("No magic header found");
    };
    // skip enough bytes to be on packet boundary
    let skip = start;
    stream
        .read_exact(&mut packet_buffer[..skip])
        .await
        .context("Failed to read from stream")?;

    println!("Synchronized to packet boundary");
    Ok(())
}

/// Process a batch of packets for data collection
pub async fn do_batch(
    stream: &mut (dyn AsyncRead + Unpin),
    csv_logger: &mut CsvLogger,
    gui: &LabrecordApp,
) -> Result<()> {
    let batch_start = Instant::now() - TIME_PER_SAMPLE * BURST_SAMPLES as u32;

    let mut packet_buffer = vec![0u8; PACKET_SIZE];
    let mut sensor1_samples = Vec::new();
    let mut sensor2_samples = Vec::new();

    // Synchronize to packet boundary by finding "ADXL" header
    for i in 0..10 {
        println!("Synchronizing to packet boundary... (try {})", i);
        if let Err(e) = synchronize_to_packet_boundary(stream).await {
            println!("Failed to synchronize: {}", e);
            if i == 9 {
                anyhow::bail!("Failed to synchronize after 10 tries");
            }
        } else {
            break;
        }
    }

    // Process packets for one hour (approximately)
    let packets_per_hour = (SAMPLES_PER_SECOND * 60 * 60) / BURST_SAMPLES;

    for packet_num in 0..packets_per_hour {
        // Read one complete packet
        stream
            .read_exact(&mut packet_buffer)
            .await
            .context("Failed to read packet")?;

        // Debug: Print bytes of every 100th packet to see what we're getting
        if packet_num % 100 == 0 {
            println!("Raw packet {} data: {:02X?}", packet_num, &packet_buffer);
        }

        // Parse the packet using the wire module
        let packet = match wire::parse_uart_packet(&packet_buffer) {
            Ok(p) => p,
            Err(e) => {
                println!("Failed to parse packet {}: {}", packet_num, e);
                data_print(&packet_buffer);
                // Try to resynchronize
                break;
            }
        };

        // Convert packet to samples
        let samples = packet_to_samples(&packet);

        // Debug: Print packet info occasionally (copy values to avoid packed struct issues)
        if packet_num % 100 == 0 {
            let sensor_id = packet.sensor_id;
            let sample_count = packet.sample_count;
            let timestamp = packet.timestamp;
            println!(
                "Packet {}: Sensor {} with {} samples at timestamp {}",
                packet_num, sensor_id, sample_count, timestamp
            );
        }

        // Store samples and process immediately based on sensor ID
        let sensor_id = packet.sensor_id;
        let now = Instant::now();

        match sensor_id {
            1 => {
                // Process Sensor 1 samples immediately
                for (i, sample) in samples.iter().enumerate() {
                    let time = now - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
                    let time_offset = time - batch_start;

                    // Update GUI immediately
                    add_samples(&gui.sensor1, time_offset, sample).await;
                }
                sensor1_samples = samples;
            }
            2 => {
                // Process Sensor 2 samples immediately
                for (i, sample) in samples.iter().enumerate() {
                    let time = now - (samples.len() - i) as u32 * TIME_PER_SAMPLE;
                    let time_offset = time - batch_start;

                    // Update GUI immediately
                    add_samples(&gui.sensor2, time_offset, sample).await;
                }
                sensor2_samples = samples;
            }
            _ => {
                println!("Warning: Invalid sensor ID: {}", sensor_id);
                continue;
            }
        }

        // Log to CSV when we have samples from both sensors (for dual-sensor CSV format)
        if !sensor1_samples.is_empty() && !sensor2_samples.is_empty() {
            // Log all samples from this burst
            for i in 0..BURST_SAMPLES
                .min(sensor1_samples.len())
                .min(sensor2_samples.len())
            {
                let time = now - (BURST_SAMPLES - i) as u32 * TIME_PER_SAMPLE;
                let time_offset = time - batch_start;

                csv_logger
                    .log(time_offset, &sensor1_samples[i], &sensor2_samples[i])
                    .await
                    .context("Failed to log sample")?;
            }

            // Clear the samples after logging to CSV
            sensor1_samples.clear();
            sensor2_samples.clear();
        }

        // Print progress occasionally
        if packet_num % 1000 == 0 {
            println!("Processed {} packets", packet_num);
        }
    }

    Ok(())
}
